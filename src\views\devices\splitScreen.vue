<template>
	<div class="layout-pd splice">
		<el-radio-group
			v-model="state.tableData.pageParams.size"
			style="margin-bottom: 10px"
			@change="onHandleSizeChange"
		>
			<template v-if="deviceType === '-100'">
				<el-radio-button :label="3">六分屏</el-radio-button>
				<el-radio-button :label="6">十二分屏</el-radio-button>
			</template>
			<template v-else>
				<el-radio-button :label="6">六分屏</el-radio-button>
				<el-radio-button :label="12">十二分屏</el-radio-button>
			</template>
		</el-radio-group>
		<div class="video-container">
			<div
				v-if="state.tableData.data.length"
				v-for="(item, $index) in state.tableData.data"
				:key="item.id + '_' + state.tableData.pageParams.size"
				:id="item.id"
				class="video-item"
				:class="{
					video6:
						deviceType === '-100'
							? state.tableData.pageParams.size == 3
							: state.tableData.pageParams.size == 6,
					video12:
						deviceType === '-100'
							? state.tableData.pageParams.size == 6
							: state.tableData.pageParams.size == 12,
				}"
			>
				<Jessibuca
					:id="'jess' + $index"
					:deviceId="item.id"
					:name="item.name"
					:coverUrl="item.coverUrl"
					:ifControl="item.ifControl"
					:deviceRtsp="item.deviceRtsp"
					:manufacturer="item.manufacturer"
					:onlineStatus="item.onlineStatus || 1"
					@recoveredFromNoSignal="handleRecoveredFromNoSignal"
				/>
			</div>
			<div v-else class="empty">暂无数据</div>
		</div>
		<el-pagination
			@size-change="onHandleSizeChange"
			@current-change="onHandleCurrentChange"
			v-model:current-page="state.tableData.pageParams.page"
			v-model:page-size="state.tableData.pageParams.size"
			background
			layout="prev, pager, next, jumper"
			:total="state.tableData.total"
		>
		</el-pagination>
	</div>
</template>
<script lang="ts" setup>
import { defineAsyncComponent, reactive, onMounted } from 'vue';
import { getFireDevices, getDevices } from '/@/api/devices';
import { useRoute } from 'vue-router';

const Jessibuca = defineAsyncComponent(() => import('./jessibuca copy.vue'));

type FireDeviceInfo = {
	id: string;
	name: string;
	deviceRtsp: string;
	manufacturer: number;
	ifControl: number;
	coverUrl: string;
	onlineStatus?: number; // 添加在线状态字段，可选
};

type DeviceType = '-100' | '1';

const route = useRoute();
const deviceType = (route.query.deviceType as DeviceType) || '1';

const state = reactive<ViewBaseState<FireDeviceInfo>>({
	tableData: {
		filter: {
			sort: 'num,asc',
			deviceType,
		},
		data: [],
		total: 0,
		loading: false,
		pageParams: {
			page: 1,
			size: deviceType === '-100' ? 3 : 6,
		},
	},
	selectIds: [],
});

const getDeviceData = async () => {
	const { page, size } = state.tableData.pageParams;
	const query = {
		page: page - 1,
		size,
		...state.tableData.filter,
	};

	let response;

	switch (deviceType) {
		case '-100':
			response = await getFireDevices(query);
			break;
		case '1':
			response = await getDevices({
				...query,
				typeFilter: 1,
			});
			break;
	}

	const payload = response.payload;
	const content = payload.content || [];

	state.tableData.data = [];

	if (deviceType === '-100') {
		content.forEach((item: FireDeviceRow) => {
			if (item.infraredId && item.infraredVideoUrl) {
				state.tableData.data.push({
					id: item.infraredId,
					name: `${item.name}-热成像`,
					deviceRtsp: item.infraredVideoUrl,
					ifControl: item.ifControl || 0,
					manufacturer: item.manufacturer,
					coverUrl: item.infraredVideoCoverUrl,
					onlineStatus: item.onlineStatus || 1, // 默认为在线状态
				});
			}

			if (item.visibleLightId && item.visibleLightVideoUrl) {
				state.tableData.data.push({
					id: item.visibleLightId,
					name: `${item.name}-可见光`,
					ifControl: item.ifControl || 0,
					deviceRtsp: item.visibleLightVideoUrl,
					manufacturer: item.manufacturer,
					coverUrl: item.visibleVideoCoverUrl,
					onlineStatus: item.onlineStatus || 1, // 默认为在线状态
				});
			}
		});
	} else {
		content.forEach((item: any) => {
			item.channels?.forEach((channel: any) => {
				if (channel.id && channel.previewUrl) {
					state.tableData.data.push({
						id: channel.id,
						name: channel.channelName || channel.name,
						deviceRtsp: channel.previewUrl,
						ifControl: channel.ifControl || 0,
						manufacturer: channel.manufacturer,
						coverUrl: channel.videoCoverUrl,
					});
				}
			});
		});
	}

	state.tableData.total = payload.totalElements;
	state.tableData.loading = false;
};

onMounted(() => {
	getDeviceData();
});

const onHandleSizeChange = (val: number) => {
	state.tableData.pageParams.size = val;
	getDeviceData();
};

const onHandleCurrentChange = (val: number) => {
	state.tableData.pageParams.page = val;
	getDeviceData();
};

// 处理设备从无信号状态恢复的事件
const handleRecoveredFromNoSignal = (deviceId: string) => {
	console.log(`设备 ${deviceId} 从无信号状态恢复`);
	// 这里可以添加额外的处理逻辑，比如更新设备状态、发送通知等
};
</script>

<style lang="scss">
.layout-main {
	height: calc(100% - 85px) !important;
}

.splice {
	height: 100%;
	display: flex;
	flex-direction: column;
}
.video-container {
	flex: 1;
	height: 100%;
	display: flex;
	flex-wrap: wrap;
	align-content: flex-start;

	.video-item {
		background: rgba(13, 14, 27);
		margin: 0 10px 10px 0;
	}

	.video6 {
		width: calc((100% - 20px) / 3);
		aspect-ratio: 16 / 9;
		&:nth-child(3n) {
			margin-right: 0;
		}
		&:nth-child(n + 4) {
			margin-bottom: 0;
		}
	}
	.video12 {
		width: calc((100% - 30px) / 4);
		aspect-ratio: 16 / 9;
		&:nth-child(4n) {
			margin-right: 0;
		}
		&:nth-last-child(n + 9) {
			margin-bottom: 0;
		}
	}
}
.empty {
	width: 100%;
	margin-top: 40px;
	display: flex;
	justify-content: center;
	color: var(--el-text-color-secondary);
}
</style>
