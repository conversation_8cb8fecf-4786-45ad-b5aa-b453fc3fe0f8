import { RouteRecordRaw } from 'vue-router';
import {
	homeRoute as defaultHomeRoute,
	staticRoutes as defaultStaticRoutes,
} from './modules/default';
import { homeRoute as sdHomeRoute, staticRoutes as sdStaticRoutes } from './modules/sd';
import { homeRoute as robotHomeRoute, staticRoutes as robotStaticRoutes } from './modules/robot';

// 不同项目，生成不同的路由表
let homeRoute: RouteRecordRaw;
let systemStaticRoutes: RouteRecordRaw[] = [];

if (window.GLOBAL_CONFIG.system === 'sd') {
	homeRoute = sdHomeRoute;
	systemStaticRoutes = sdStaticRoutes;
} else if (window.GLOBAL_CONFIG.system === 'robot') {
	homeRoute = robotHomeRoute;
	systemStaticRoutes = robotStaticRoutes;
} else {
	homeRoute = defaultHomeRoute;
	systemStaticRoutes = defaultStaticRoutes;
}

/**
 * 建议：路由 path 路径与文件夹名称相同，找文件可浏览器地址找，方便定位文件位置
 *
 * 路由meta对象参数说明
 * meta: {
 *      title:          菜单栏及 tagsView 栏、菜单搜索名称（国际化）
 *      isLink：        是否超链接菜单，开启外链条件，`1、isLink: 链接地址不为空 2、isIframe:false`
 *      isHide：        是否隐藏此路由
 *      isKeepAlive：   是否缓存组件状态
 *      isAffix：       是否固定在 tagsView 栏上
 *      isIframe：      是否内嵌窗口，开启条件，`1、isIframe:true 2、isLink：链接地址不为空`
 *      permissions：   当前路由权限标识，控制路由显示、隐藏
 *      icon：          菜单、tagsView 图标，阿里：加 `iconfont xxx`，fontawesome：加 `fa xxx`
 * }
 */
// 扩展 RouteMeta 接口
declare module 'vue-router' {
	interface RouteMeta {
		title?: string;
		isLink?: string;
		isHide?: boolean;
		isKeepAlive?: boolean;
		isAffix?: boolean;
		isIframe?: boolean;
		permissions?: string[];
		icon?: string;
		fullScreenIcon?: string;
	}
}

/**
 * 定义动态路由
 * 前端添加路由，请在顶级节点的 `children 数组` 里添加
 * @description 未开启 isRequestRoutes 为 true 时使用（前端控制路由），开启时第一个顶级 children 的路由将被替换成接口请求回来的路由数据
 * @description 各字段请查看 `/@/views/system/menu/component/addMenu.vue 下的 ruleForm`
 * @returns 返回路由菜单数据
 */
export const dynamicRoutes: Array<RouteRecordRaw> = [
	{
		path: '/',
		name: 'Screen',
		component: () => import('/@/layout/index.vue'),
		redirect: '/screen',
		meta: {
			isKeepAlive: true,
		},
		children: [
			{ ...homeRoute },
			{
				path: '/monitorEvents',
				name: 'MonitorEvents',
				meta: {
					title: '事件一览',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-wenjuan-copy',
					permissions: ['*:*:*', 'monitor-event-files:*:*'],
					component: () => import('/@/views/monitorEvents/index.vue'),
				},
				children: [],
			},
			{
				path: '/aiModels',
				name: 'AiModels',
				component: () => import('/@/views/aiModels/index.vue'),
				meta: {
					title: '模型管理',
					isHide: true,
					isKeepAlive: false,
					isAffix: false,
					icon: 'ele-Coin',
					// permissions: ['*:*:*', 'ai-models:view:*'],
				},
			},
			{
				path: '/device',
				name: 'Device',
				redirect: '/device/infrared',
				meta: {
					title: '设备管理',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-shexiangtou-copy',
					permissions: ['*:*:*', 'devices:*:*'],
				},
				children: [
					{
						path: '/device/infrared',
						name: 'InfraredDevice',
						component: () => import('/@/views/devices/index.vue'),
						props: {
							deviceType: 2,
						},
						meta: {
							title: '红外设备',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-hongwaixiangji',
							permissions: ['*:*:*', 'devices:*:*'],
						},
					},
					{
						path: '/device/monitoring',
						name: 'MonitoringDevice',
						component: () => import('/@/views/devices/index.vue'),
						props: {
							deviceType: 1,
						},
						meta: {
							title: '监控设备',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-jiankongshebeijieru',
							permissions: ['*:*:*', 'devices:*:*'],
						},
						children: [
							{
								path: '/device/rvc',
								name: 'RVCDevice',
								component: () => import('/@/views/devices/rvc.vue'),
								meta: {
									title: '实时视频',
									isHide: true,
									isKeepAlive: true,
									isAffix: false,
									icon: 'iconfont icon-hongwaixiangji',
									permissions: ['*:*:*', 'devices:*:*'],
								},
							},
							{
								path: '/device/fire/splitScreen',
								name: 'SplitScreen',
								component: () => import('/@/views/devices/splitScreen.vue'),
								meta: {
									title: '分屏',
									isHide: true,
									isKeepAlive: true,
									isAffix: false,
									icon: 'iconfont icon-hongwaixiangji',
									permissions: ['*:*:*', 'devices:*:*'],
								},
							},
						],
					},
					// {
					//   path: '/device/patrol',
					//   name: 'PatrolDevice',
					//   component: () => import('/@/views/devices/index.vue'),
					//   props: {
					//     deviceType: 4,
					//   },
					//   meta: {
					//     title: '巡护设备',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-jiqiren',
					//     permissions: ['*:*:*', 'devices:*:*'],
					//   },
					// },
					// {
					//   path: '/device/fire',
					//   name: 'FireDevice',
					//   component: () => import('/@/views/devices/index.vue'),
					//   props: {
					//     deviceType: -100,
					//   },
					//   meta: {
					//     title: '防火设备',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-rewen',
					//     permissions: ['*:*:*', 'devices:*:*'],
					//   },

					//   children: [

					//   ]
					// },
				],
			},
			{
				path: '/monitors',
				name: 'Monitors',
				component: () => import('/@/views/monitors/index.vue'),
				meta: {
					title: '监测任务编排',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-renwuguanli',
					permissions: ['*:*:*', 'monitors:*:*'],
				},
			},
			{
				path: '/recog',
				name: 'Recog',
				redirect: '/recog/infraredDevice',
				meta: {
					title: 'AI智能识别',
					isHide: true,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-AIchuangguan-copy',
					// permissions: ['*:*:*', 'identify:*:*', 'devices:view:*'],
				},
				children: [
					{
						path: '/recog/infraredDevice',
						name: 'RecogInfraredDevice',
						component: () => import('/@/views/recognize/index.vue'),
						meta: {
							title: '红外设备数据识别',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-hongwaixiangji',
							// permissions: ['*:*:*', 'devices:*:*'],
						},
					},
					{
						path: '/recog/testDevice',
						name: 'RecogTestDevice',
						component: () => import('/@/views/recognize/index.vue'),
						meta: {
							title: '测试数据识别',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'ele-Link',
							// permissions: ['*:*:*', 'identify:*:*'],
						},
					},
				],
			},

			// {
			//   path: '/moment',
			//   name: 'Moment',
			//   component: () => import('/@/views/moment/index.vue'),
			//   meta: {
			//     title: '精选图集',
			//     isHide: false,
			//     isKeepAlive: true,
			//     isAffix: false,
			//     icon: 'ele-Camera',
			//     permissions: ['*:*:*', 'moments:*:*'],
			//   },
			// },
			{
				path: '/rawMaterials',
				name: 'RawMaterials',
				redirect: '/rawMaterials/hwsc',
				meta: {
					title: '原始数据',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-tupiansucai1',
					permissions: ['*:*:*', 'images:*:*'],
				},
				children: [
					{
						path: '/rawMaterials/hwsc',
						name: 'RawMaterials1',
						component: () => import('/@/views/rawMaterials/index.vue'),
						props: {
							typeFilter: 2,
						},
						meta: {
							title: '红外感知抓拍',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							// icon: 'iconfont icon-hongwaixiangji',
							icon: '/menuSvgs/hwgz.svg',
							permissions: ['*:*:*', 'images:*:*'],
						},
					},
					{
						path: '/rawMaterials/jkzp',
						name: 'RawMaterials2',
						component: () => import('/@/views/rawMaterials/snap.vue'),
						props: {
							typeFilter: 1,
						},
						meta: {
							title: '监控定时抓拍',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							// icon: 'iconfont icon-jiankongshebeijieru',
							icon: '/menuSvgs/jk.svg',
							permissions: ['*:*:*', 'images:*:*'],
						},
					},
					// {
					//   path: '/rawMaterials/jkxh',
					//   name: 'RawMaterials3',
					//   component: () => import('/@/views/rawMaterials/index.vue'),
					//   props: {
					//     typeFilter: 3,
					//   },
					//   meta: {
					//     title: '监控巡航抓拍',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     // icon: 'iconfont icon-side_zhinengxunhang',
					//     icon: '/menuSvgs/jkxh.svg',
					//     permissions: ['*:*:*', 'images:*:*'],
					//   },
					// },
					// {
					//   path: '/rawMaterials/xhzp',
					//   name: 'RawMaterials4',
					//   component: () => import('/@/views/rawMaterials/snap.vue'),
					//   props: {
					//     typeFilter: 4,
					//   },
					//   meta: {
					//     title: '巡护定时抓拍',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     // icon: 'iconfont icon-kaishixunhu',
					//     icon: '/menuSvgs/xh.svg',
					//     permissions: ['*:*:*', 'images:*:*'],
					//   },
					// },
					// {
					//   path: '/rawMaterials/xhsc',
					//   name: 'RawMaterials5',
					//   component: () => import('/@/views/rawMaterials/index.vue'),
					//   props: {
					//     typeFilter: 5,
					//   },
					//   meta: {
					//     title: '巡护感知抓拍',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     // icon: 'iconfont icon-jiqiren',
					//     icon: '/menuSvgs/xhgz.svg',
					//     permissions: ['*:*:*', 'images:*:*'],
					//   },
					// },
					// {
					//   path: '/rawMaterials/fire',
					//   name: 'FireMaterials',
					//   component: () => import('/@/views/rawMaterials/fire.vue'),
					//   meta: {
					//     title: '防火抓拍',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-rewen',
					//     permissions: ['*:*:*', 'images:*:*'],
					//   },
					// },
					// {
					//   path: '/rawMaterials/test',
					//   name: 'RawMaterials6',
					//   component: () => import('/@/views/rawMaterials/index.vue'),
					//   props: {
					//     typeFilter: 6,
					//   },
					//   meta: {
					//     title: '测试数据',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'ele-Link',
					//     permissions: ['*:*:*', 'images:*:*'],
					//   },
					// },
				],
			},
			{
				path: '/rawMaterials/videoRec',
				name: 'RawMaterialsVideoRec',
				component: () => import('/@/views/rawMaterials/videoRec.vue'),
				meta: {
					title: '原始视频识别记录',
					isHide: true,
					isKeepAlive: false,
					isAffix: false,
					permissions: ['*:*:*', 'images:*:*'],
				},
			},
			{
				path: '/species',
				name: 'Species',
				redirect: '/species/plant',
				meta: {
					title: '物种资源库',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'iconfont icon-muses-icon-menu1',
					permissions: ['*:*:*', 'directory:*:*'],
				},
				children: [
					{
						path: '/species/plant',
						name: 'SpeciesPlant',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '植物',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-plant-copy',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/animal',
						name: 'SpeciesAnimal',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '哺乳类',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-animal',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/fish',
						name: 'SpeciesFish',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '鱼类',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-haixianyu',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/amphibians',
						name: 'SpeciesAmphibians',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '两栖类',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-liangqilei',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/reptile',
						name: 'SpeciesReptile',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '爬行类',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-pahanglei',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/bird',
						name: 'SpeciesBird',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '鸟类',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-bird',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					// {
					//   path: '/species/bird/precious',
					//   name: 'PreciousBird',
					//   component: () => import('/@/views/species/tag.vue'),
					//   props: {
					//     tag: 2,
					//   },
					//   meta: {
					//     title: '珍稀鸟类',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-frequentbuyers',
					//     permissions: ['*:*:*', 'animals:*:*'],
					//     indent: 20,
					//   },
					// },
					// {
					//   path: '/species/bird/specific',
					//   name: 'SpecificBird',
					//   component: () => import('/@/views/species/tag.vue'),
					//   props: {
					//     tag: 1,
					//   },
					//   meta: {
					//     title: '特定鸟类',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-tedingzongjiaoxinyang',
					//     permissions: ['*:*:*', 'animals:*:*'],
					//     indent: 20,
					//   },
					// },
					// {
					//   path: '/species/bird/nmg',
					//   name: 'NmgBird',
					//   component: () => import('/@/views/species/tag.vue'),
					//   props: {
					//     tag: 0,
					//   },
					//   meta: {
					//     title: '内蒙古鸟类',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-neimenggu',
					//     permissions: ['*:*:*', 'animals:*:*'],
					//     indent: 20,
					//   },
					// },
					// {
					//   path: '/species/bird/resident',
					//   name: 'Resident',
					//   component: () => import('/@/views/species/tag.vue'),
					//   props: {
					//     residencyType: 1,
					//   },
					//   meta: {
					//     title: '留鸟',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-juzhudi',
					//     permissions: ['*:*:*', 'animals:*:*'],
					//     indent: 20,
					//   },
					// },
					// {
					//   path: '/species/bird/migratory',
					//   name: 'Migratory',
					//   component: () => import('/@/views/species/tag.vue'),
					//   props: {
					//     residencyType: 2,
					//   },
					//   meta: {
					//     title: '候鸟',
					//     isHide: false,
					//     isKeepAlive: true,
					//     isAffix: false,
					//     icon: 'iconfont icon-qihou-01',
					//     permissions: ['*:*:*', 'animals:*:*'],
					//     indent: 20,
					//   },
					// },
					{
						path: '/species/insect',
						name: 'SpeciesInsect',
						component: () => import('/@/views/species/index.vue'),
						meta: {
							title: '昆虫',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							icon: 'iconfont icon-insect',
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
					{
						path: '/species/:species/:subSpecies?/detail',
						name: 'SpeciesDetail',
						component: () => import('/@/views/species/detail.vue'),
						meta: {
							title: '物种详情',
							isHide: true,
							isKeepAlive: false,
							isAffix: false,
							permissions: ['*:*:*', 'directory:*:*'],
						},
					},
				],
			},
			// {
			//   path: '/patrol',
			//   name: 'Patrol',
			//   redirect: '/patrol/task',
			//   meta: {
			//     title: '巡护管理',
			//     isHide: false,
			//     isKeepAlive: true,
			//     isAffix: false,
			//     icon: 'iconfont icon-xunhuguanli',
			//     permissions: ['*:*:*', 'robot-patrol-tasks:*:*', 'robot-patrol-plans:*:*', 'robot-maps:*:*'],
			//   },
			//   children: [
			//     {
			//       path: '/patrol/task',
			//       name: 'PTask',
			//       component: () => import('/@/views/patrol/task.vue'),
			//       meta: {
			//         title: '巡检任务',
			//         isHide: false,
			//         isKeepAlive: true,
			//         isAffix: false,
			//         icon: 'iconfont icon-xunjianrenwu',
			//         permissions: ['*:*:*', 'robot-patrol-tasks:*:*'],
			//       },
			//     },
			//     {
			//       path: '/patrol/plan',
			//       name: 'PPlan',
			//       component: () => import('/@/views/patrol/plan.vue'),
			//       meta: {
			//         title: '巡检计划',
			//         isHide: false,
			//         isKeepAlive: true,
			//         isAffix: false,
			//         icon: 'iconfont icon-xunjianjihua',
			//         permissions: ['*:*:*', 'robot-patrol-plans:*:*'],
			//       },
			//     },
			//     {
			//       path: '/patrol/line',
			//       name: 'PLine',
			//       component: () => import('/@/views/patrol/line.vue'),
			//       meta: {
			//         title: '巡检路线',
			//         isHide: false,
			//         isKeepAlive: true,
			//         isAffix: false,
			//         icon: 'iconfont icon-xunjianluxian',
			//         permissions: ['*:*:*', 'robot-maps:*:*'],
			//       },
			//     },

			//   ]
			// },
			// {
			//   path: '/liveManage',
			//   name: 'LiveManage',
			//   component: () => import('/@/views/liveManage/index.vue'),
			//   meta: {
			//     title: '直播管理',
			//     isHide: false,
			//     isKeepAlive: true,
			//     isAffix: false,
			//     icon: 'iconfont icon-zhibo',
			//     permissions: ['*:*:*', 'lives:*:*'],
			//   },
			// },
			// {
			//   path: '/alarm',
			//   name: 'Alarm',
			//   redirect: '/alarm/email',
			//   meta: {
			//     title: '通知管理',
			//     isHide: false,
			//     isKeepAlive: true,
			//     isAffix: false,
			//     icon: 'iconfont icon-tongzhiguanli1',
			//     permissions: ['*:*:*', 'recipient-emails:view:*'],
			//   },
			//   children: [
			//     {
			//       path: '/alarm/email',
			//       name: 'AlarmEmail',
			//       component: () => import('/@/views/alarm/email/index.vue'),
			//       meta: {
			//         title: '邮箱管理',
			//         isHide: false,
			//         isKeepAlive: true,
			//         isAffix: false,
			//         icon: 'iconfont icon-youxiangguanli',
			//         permissions: ['*:*:*', 'recipient-emails:view:*'],
			//       },
			//     },
			//   ],
			// },
			{
				path: '/messages',
				name: 'Messages',
				component: () => import('/@/views/messages/index.vue'),
				meta: {
					title: '消息通知',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'ele-Message',
					permissions: ['*:*:*', 'messages:*:*'],
				},
			},
			{
				path: '/setting',
				name: 'Setting',
				redirect: '/setting/users',
				meta: {
					title: '系统管理',
					isHide: false,
					isKeepAlive: true,
					isAffix: false,
					icon: 'ele-Setting',
					permissions: [
						'*:*:*',
						'operation-system:*:*',
						'users:*:*',
						'authz:*:*',
						'audit-logs:*:*',
					],
				},
				children: [
					{
						path: '/setting/users',
						name: 'Users',
						component: () => import('/@/views/setting/users/index.vue'),
						meta: {
							title: '用户管理',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							// permissions: ['*:*:*', 'operation-system:*:*', 'users:*:*', 'authz:*:*', 'audit-logs:*:*'],
							icon: 'ele-User',
						},
					},
					{
						path: '/setting/auth',
						name: 'Auth',
						component: () => import('/@/views/setting/auth/index.vue'),
						meta: {
							title: '用户权限管理',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							// permissions: ['*:*:*', 'operation-system:*:*', 'users:*:*', 'authz:*:*', 'audit-logs:*:*'],
							icon: 'ele-Unlock',
						},
					},
					{
						path: '/setting/auditLog',
						name: 'AuditLog',
						component: () => import('/@/views/setting/auditLog/index.vue'),
						meta: {
							title: '审计日志管理',
							isLink: '',
							isHide: false,
							isKeepAlive: true,
							isAffix: false,
							isIframe: false,
							// permissions: ['*:*:*', 'audit-logs:*:*'],
							icon: 'ele-Tickets',
						},
					},
				],
			},
		],
	},
];

/**
 * 定义404、401界面
 * @link 参考：https://next.router.vuejs.org/zh/guide/essentials/history-mode.html#netlify
 */
export const notFoundAndNoPower = [
	{
		path: '/:path(.*)*',
		name: 'notFound',
		component: () => import('/@/views/error/404.vue'),
		meta: {
			title: '找不到此页面',
			isHide: true,
		},
	},
	{
		path: '/401',
		name: 'noPower',
		component: () => import('/@/views/error/401.vue'),
		meta: {
			title: '没有权限',
			isHide: true,
		},
	},
];

/**
 * 定义静态路由（默认路由）
 * 此路由不要动，前端添加路由的话，请在 `dynamicRoutes 数组` 中添加
 * @description 前端控制直接改 dynamicRoutes 中的路由，后端控制不需要修改，请求接口路由数据时，会覆盖 dynamicRoutes 第一个顶级 children 的内容（全屏，不包含 layout 中的路由出口）
 * @returns 返回路由菜单数据
 */
export const staticRoutes: Array<RouteRecordRaw> = [
	{
		path: '/login',
		name: 'Login',
		component: () => import('/@/views/login/index.vue'),
		meta: {
			title: '登录',
		},
	},
	...systemStaticRoutes,
];
