<template>
	<div
		class="player"
		:id="props.deviceId"
		ref="container"
		:style="{ backgroundImage: playShow ? 'url(' + props.coverUrl + ')' : '' }"
	>
		<div v-show="jessibucaState.errorStatus" class="jessibuca-error">
			<div class="jessibuca-error-icon" v-if="jessibucaState.errorStatus !== 3">
				<img src="/src/assets/video-play-error.png" alt="" />
			</div>
			<div class="jessibuca-error-text">
				{{ errorStatusText }}
				<el-link class="jessibuca-error-text-link" type="primary" @click="retry">重试</el-link>
			</div>
		</div>
		<span class="video-title">{{ props.name }}</span>
		<!-- 播放按钮 -->
		<span v-if="playShow" class="video-play" @click="play"></span>

		<!-- 控制按钮和面板 - 自定义实现替代tooltip -->
		<i
			v-show="isFullscreen && props.ifControl !== 0 && kongzhiControlShow"
			ref="kongzhiControl"
			class="iconfont icon-kongzhi font16 kongzhiControl"
			@click="toggleControlPanel"
		></i>

		<!-- 替换原来的自定义控制面板为VideoControlPanel组件 -->
		<VideoControlPanel
			v-model:visible="controlPanelVisible"
			:panel-style="controlPanelStyle"
			speed-value-color="#ffffff"
			v-model:playback-rate="playbackRate"
			@key-action="handleKeyAction"
		/>
	</div>
</template>
·

<script setup lang="ts">
import {
	ref,
	onDeactivated,
	onUnmounted,
	onActivated,
	computed,
	reactive,
	watch,
	onMounted,
	nextTick,
} from 'vue';
import { generatePlayUrl, controlCamera } from '/@/api/devices';
// 导入VideoControlPanel组件
import VideoControlPanel from '/@/components/video/VideoControlPanel.vue';
const emit = defineEmits(['recoveredFromNoSignal']);
const props = defineProps({
	deviceId: { type: String, required: true },
	name: { type: String, required: true },
	coverUrl: { type: String, required: true },
	ifControl: { type: Number, required: true, default: 0 },
	manufacturer: { type: Number, required: true },
	deviceRtsp: { type: String, required: true },
	onlineStatus: {
		type: Number,
		required: true,
		default: 0,
		validator: (value: number) => [0, 1].includes(value),
	},
});
const container = ref();
const tooltipRef = ref();
const kongzhiControl = ref();
const kongzhiControlShow = ref(false);
const playShow = ref(true);
const controlPanelVisible = ref(false);
const controlPanelStyle = ref({});
const playbackRate = ref(2); // 添加播放速度状态，默认为2
const isFullscreen = ref(false);
let jessibuca: any;
let playUrl: string;

const jessibucaState = reactive({
	errorStatus: 0, // 错误状态码：1 超时 2 加载出错
});
const errorStatusText = computed(() => {
	if (jessibucaState.errorStatus === 1) return '加载超时，请检查设备情况';
	if (jessibucaState.errorStatus === 2) return '加载出错，请检查设备状态';
	if (jessibucaState.errorStatus === 3) return '无信号';
	return '';
});

const isDeviceOnline = computed(() => {
	return props.onlineStatus === 1;
});
// 监听设备在线状态
watch(
	() => props.onlineStatus,
	(newStatus) => {
		console.log('newStatus', newStatus);
		if (newStatus !== 1) {
			// 设备离线时设置错误状态
			jessibucaState.errorStatus = 3;
			playShow.value = false;
		} else if (jessibucaState.errorStatus === 3) {
			// 如果设备恢复在线且当前是"无信号"错误，则清除错误
			jessibucaState.errorStatus = 0;
			playShow.value = true;
		}
	},
	{ immediate: true }
);

const maxRetryTimes = 3;
let retryTimes = 0;
const initJessibuca = () => {
	jessibuca = new (window as any).Jessibuca({
		container: container.value,
		videoBuffer: 1, // 缓存时长
		isResize: true,
		isFullResize: true,
		isFlv: true,
		loadingText: '视频加载中，请稍候',
		loadingTimeout: 20, //在连接成功之前,如果超过设定时长无数据返回,则回调timeout事件
		loadingTimeoutReplay: true, // 连接成功之前,如果超过设定时长无数据返回,是否重试
		loadingTimeoutReplayTimes: 3, // 连接成功之前,如果超过设定时长无数据返回,重试次数
		heartTimeout: 20, // 播放中途,如果超过设定时长无数据返回,则回调timeout事件
		heartTimeoutReplay: true, // 播放中途,如果超过设定时长无数据返回,是否重试
		heartTimeoutReplayTimes: 3, // 播放中途,如果超过设定时长无数据返回,重试次数
		decoder: '/jessibuca/decoder.js',
		hasAudio: true,
		debug: false,
		operateBtns: {
			fullscreen: true,
			screenshot: false,
			play: true,
			audio: false,
			record: false,
		},
		forceNoOffscreen: false,
		isNotMute: true,
		useWebFullScreen: true,
		controlAutoHide: true,
	});
	jessibuca.setScaleMode(1);
	jessibuca.on('load', function () {
		console.log('load');
	});
	jessibuca.on('start', function () {
		console.log('start render');
		retryTimes = 0;
		kongzhiControlShow.value = true;
		// 无信号恢复
		if (noSignalRetryStatus) {
			handleRecoverFromNoSignal();
		}
	});

	jessibuca.on('pause', function () {
		tooltipRef.value?.hide();
		kongzhiControlShow.value = false;
	});
	jessibuca.on('fullscreen', function (flag: boolean) {
		console.log('is fullscreen', flag);
		isFullscreen.value = flag;
	});

	jessibuca.on('timeout', function (error: any) {
		console.log('jessibuca ==== timeout', error);
		// 连接超时
		if (error === 'loadingTimeout') {
			retryTimes++;
			// 重试三次
			if (retryTimes >= maxRetryTimes) {
				destoryJessibuca();
				jessibucaState.errorStatus = 1;
				playShow.value = false;
				retryTimes = 0;
			}
		}
	});

	jessibuca.on('error', function (error: any) {
		console.log('jessibuca ==== error', error);
		if (noSignalRetryStatus) {
			handleRecoverFromNoSignal();
		}
		destoryJessibuca();
		jessibucaState.errorStatus = 2;
		playShow.value = false;
		kongzhiControlShow.value = false;
		retryTimes = 0;
	});

	let leftElement = container.value.querySelector('.jessibuca-controls-left');
	let rightElement = container.value.querySelector('.jessibuca-controls-right');
	swapChildren(rightElement, leftElement);

	leftElement.appendChild(kongzhiControl.value);
};

/**
 * 获取播放状态
 * @returns
 */
const getPlayStatus = () => {
	return jessibuca?.isPlaying() || false;
};

const play = () => {
	console.log('play', props.onlineStatus);
	playShow.value = false;
	initJessibuca();
	playUrl = generatePlayUrl(<string>props.deviceId);
	jessibuca.play(playUrl);
};
// 无信号重试状态
let noSignalRetryStatus = false;
// 无信号恢复的处理函数
const handleRecoverFromNoSignal = () => {
	noSignalRetryStatus = false;
	emit('recoveredFromNoSignal', props.deviceId);
};
const retry = () => {
	// 如果设备无信号，保存无信号状态，用来恢复无信号状态
	if (jessibucaState.errorStatus === 3) {
		noSignalRetryStatus = true;
	}
	// 先销毁实例，但保持错误状态为0（清除错误显示）
	destoryJessibuca();
	// 然后重新初始化并播放
	play();
};
// 修改为处理VideoControlPanel组件的keyAction事件
const handleKeyAction = async (payload: { commandType: number; speed: number }) => {
	await controlCamera({
		commandType: payload.commandType,
		deviceRtsp: props.deviceRtsp,
		manufacturer: props.manufacturer,
		// 如果您的API需要，可以添加速度参数
		speed: payload.speed,
	});
};
// 控制面板显示
const toggleControlPanel = () => {
	controlPanelVisible.value = !controlPanelVisible.value;

	// 当面板显示时，计算位置
	if (controlPanelVisible.value) {
		nextTick(() => {
			updateControlPanelPosition();
		});
	}
};

// 添加计算控制面板位置的方法
const updateControlPanelPosition = () => {
	if (kongzhiControl.value) {
		const buttonRect = kongzhiControl.value.getBoundingClientRect();
		const containerRect = container.value.getBoundingClientRect();

		// 计算右下角的位置
		const bottom = containerRect.bottom - buttonRect.bottom; // 在按钮上方50px
		const right = containerRect.right - buttonRect.right; // 保持在右侧对齐，微调

		controlPanelStyle.value = {
			position: 'absolute',
			bottom: `${Math.max(40, bottom)}px`, // 从底部开始定位
			right: `${Math.max(10, right)}px`, // 从右侧开始定位
			zIndex: '9999',
		};
	}
};

const clearDestroy = () => {
	destoryJessibuca(true); // 完全销毁时重置所有状态
	document.removeEventListener('click', () => {
		if (controlPanelVisible.value) {
			controlPanelVisible.value = false;
		}
	});

	window.removeEventListener('resize', () => {
		if (controlPanelVisible.value) {
			updateControlPanelPosition();
		}
	});
};
onActivated(() => {
	// console.log('onActivated');
	// 如果设备离线，则设置错误状态
	if (!isDeviceOnline.value) {
		jessibucaState.errorStatus = 3;
		playShow.value = false;
	}
});
onDeactivated(() => {
	// console.log('onDeactivated');
	destoryJessibuca(false);
});
onUnmounted(() => {
	console.log('onUnmounted');
	clearDestroy();
});
onMounted(() => {
	// console.log('onMounted');
	document.addEventListener('click', () => {
		if (controlPanelVisible.value) {
			controlPanelVisible.value = false;
		}
	});

	// 添加窗口大小变化监听，以便在调整大小时更新位置
	window.addEventListener('resize', () => {
		if (controlPanelVisible.value) {
			updateControlPanelPosition();
		}
	});
});

/**
 * 销毁Jessibuca
 * @param resetErrorStatus 是否重置错误状态
 */
const destoryJessibuca = (resetErrorStatus = true) => {
	if (resetErrorStatus) {
		jessibucaState.errorStatus = 0;
		playShow.value = true;
	} else {
		if (!jessibucaState.errorStatus) {
			// 如果错误状态为0，则不重置错误状态，保留之前的错误信息
			playShow.value = true;
		} else {
			playShow.value = false;
		}
	}
	controlPanelVisible.value = false; // 隐藏控制面板
	jessibuca && jessibuca.destroy();
	jessibuca = null;
};

function swapChildren(child1: HTMLElement, child2: HTMLElement) {
	// 保存两个子级的父节点
	var parent1 = child1.parentNode;
	var parent2 = child2.parentNode;
	// 交换两个子级
	parent1!.replaceChild(child2, child1);
	parent2!.replaceChild(child1, child2);
	// 将原始的第二个子级移动到第一个子级原来的位置
	parent1!.insertBefore(child2, child1.nextSibling);
}

defineExpose({
	play,
	getPlayStatus,
	isFullscreen,
});
</script>

<style>
.jessibuca-icon-control {
	background: url('data:image/svg+xml;base64,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')
		no-repeat 50%;
	background-size: 100% 100%;
}
</style>
<style lang="scss" scoped>
/* @import url(); 引入css类 */
.player {
	width: 100%;
	height: 100%;
	position: relative;
	background-size: 100% 100%;
	.video-title {
		position: absolute;
		left: vw(10);
		top: vw(10);
		color: #fff;
		z-index: 4;
		font-size: vw(17);
		text-shadow: 0 0 vw(6) #000;
	}
	.video-play {
		position: absolute;
		left: 50%;
		top: 50%;
		transform: translate(-50%, -50%);
		width: vw(48);
		height: vw(48);
		background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACEElEQVRoQ+2ZXStEQRjH/3/yIXwDdz7J+i7kvdisXCk3SiFJW27kglBcSFFKbqwQSa4krykuKB09Naf2Yndn5jgzc06d53Znd36/mWfeniVyHsw5PwqB0DOonYEoijYBlOpAFwCMkHwLDS/9mwhEDUCfAAyTXA4tYSLwC6CtCegegH6S56FETAR+AHRoACcBTJAUWa+RloBAXwAYIrnt0yBNgZi7qtbHgw8RFwLC/QFglOScawlXAjH3gUqrE1cirgVi7mkAYyS/0xbxJSDcdwAGSa6nKeFTIOZeUyL3aYiEEBDuLwDjJGf+KxFKIOY+BdBL8iipSGiBmHtWbbuftiJZERBuOfgGSK7aSGRJIObeUml1ayKSRQHhlgtkiaTcdltGVgUE+ppkV54FaiS78yrwqlLoOI8Cch2XV548W7WRpTVwA6DP9kGUFYEpAOUkT9LQAvtq1M+0udKkQSgBqSlJWWYxKXj8vRACK+o6bbRIdYI+Ba7U7rKjg7L53JdAhWTZBsy0rWuBXZUuNVMg23auBF7UIl2yBbJt70JAoKV6/WwLk6R9mgKSJlJ1kLTxFmkJyCla8UZd15GJQKvyumyJ8gy8DAEvfZoINPqD41EtUjmUgoaJwAaAnjrKebVI34OSq85NBNqlCAWgE0CV5GEWwI3vQlmCbcSinYFCwPEIFDPgeIC1P1/MgHaIHDf4Aydx2TF7wnKeAAAAAElFTkSuQmCC);
		background-size: 100% 100%;
		cursor: pointer;
	}
	.el-tooltip__trigger {
		color: #ccc;
		cursor: pointer;
	}

	.jessibuca-error {
		position: absolute;
		z-index: 20;
		left: 0;
		top: 0;
		right: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		z-index: 20;
		pointer-events: none;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		color: #fff;
		font-size: vw(14);
		text-align: center;
		&-icon {
			margin-bottom: vw(16);
			img {
				width: vw(36);
			}
		}
		&-text {
			display: flex;
			align-items: center;
			&-link {
				margin-left: vw(3);
				pointer-events: auto;
				.el-link__inner {
					font-size: vw(14);
				}
			}
		}
	}
	:deep(.jessibuca-icon-loading) {
		width: vw(50) !important;
		height: vw(50) !important;
	}
	:deep(.jessibuca-loading-text) {
		font-size: vw(13) !important;
		margin-top: vw(10) !important;
	}
}
.handle-tooltip {
	padding: 0;
	.handle {
		padding: vw(2) 0;
		&-keys {
			margin: 0 auto;
			width: vw(100);
			height: vw(100);
			background: url('/src/assets/sd/rvc/handle-bg.png');
			background-repeat: no-repeat;
			background-size: 100% 100%;
			position: relative;
			.key {
				position: absolute;
				cursor: pointer;
				img {
					width: 100%;
					height: 100%;
					vertical-align: middle;
					transform-origin: center;
				}
				&:hover img {
					transform: scale(1.05);
				}
			}
			.key.top {
				width: vw(28);
				top: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.bottom {
				width: vw(28);
				bottom: vw(20);
				left: 50%;
				transform: translateX(-50%);
			}
			.key.left {
				height: vw(28);
				left: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.right {
				height: vw(28);
				right: vw(20);
				top: 50%;
				transform: translateY(-50%);
			}
			.key.motion {
				width: vw(44);
				left: 50%;
				top: 50%;
				transform: translate(-50%, -50%);
			}
			.key.add {
				width: vw(24);
				right: 0;
				bottom: 0;
			}
			.key.reduce {
				width: vw(24);
				left: 0;
				bottom: 0;
			}
		}
	}
}

.kongzhiControl {
	cursor: pointer;
}
</style>
