import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import { defineConfig, loadEnv, ConfigEnv } from 'vite';
import vueSetupExtend from 'vite-plugin-vue-setup-extend-plus';
import viteCompression from 'vite-plugin-compression';
import { buildConfig } from './src/utils/build';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import { handleConfigSelection } from './src/config/configSelector';

const pathResolve = (dir: string) => {
	return resolve(__dirname, '.', dir);
};

const alias: Record<string, string> = {
	'/@': pathResolve('./src/'),
};

const viteConfig = defineConfig((mode: ConfigEnv) => {
	const env = loadEnv(mode.mode, process.cwd());
	// const httpTarget = 'http://************:28080' // 测试环境
	// const httpTarget = 'http://*************:8080'  // 晓慧
	// const httpTarget = 'http://*************:8080/'  // 邹丁
	// const httpTarget = 'http://zbd.aiplants.cn/' // 湿地生产
	// const httpTarget = 'http://sd.nbzhu.cn/'
	const httpTarget = 'http://**************:8090'; // 湿地生产
	// const httpTarget = 'http://wb.aiplants.cn/' // 机器人生产
	return {
		plugins: [
			vue(),
			vueSetupExtend(),
			viteCompression(),
			JSON.parse(env.VITE_OPEN_CDN) ? buildConfig.cdn() : null,
			createSvgIconsPlugin({
				iconDirs: [resolve(process.cwd(), 'src/icons')], //取svg的文件路径
				symbolId: 'icon-[dir]-[name]',
			}),
			{
				name: 'config-selector',
				async buildStart() {
					await handleConfigSelection(mode.command);
				},
			},
		],
		root: process.cwd(),
		resolve: { alias },
		base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
		optimizeDeps: {
			exclude: ['vue-demi'],
		},
		server: {
			host: '0.0.0.0',
			port: env.VITE_PORT as unknown as number,
			open: JSON.parse(env.VITE_OPEN),
			hmr: true,
			proxy: {
				[env.VITE_API_URL + '/directory']: {
					target: httpTarget,
					ws: true,
					changeOrigin: true,
					rewrite: (path) => path.replace(env.VITE_API_URL, ''),
				},
				// nvr设备对接，`**************` 黑龙江网络
				[env.VITE_API_URL + '/nvr']: {
					target: 'http://**************/',
					ws: true,
					changeOrigin: true,
					rewrite: (path) => path.replace(env.VITE_API_URL + '/nvr', '/my-test/nvr'),
				},
				[env.VITE_API_URL]: {
					target: httpTarget,
					ws: true,
					changeOrigin: true,
					rewrite: (path) => path.replace(env.VITE_API_URL, ''),
				},
			},
		},
		build: {
			outDir: 'dist',
			chunkSizeWarningLimit: 1500,
			rollupOptions: {
				output: {
					chunkFileNames: 'assets/js/[name]-[hash].js',
					entryFileNames: 'assets/js/[name]-[hash].js',
					assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
					manualChunks(id) {
						if (id.includes('node_modules')) {
							return (
								id.toString().match(/\/node_modules\/(?!.pnpm)(?<moduleName>[^\/]*)\//)?.groups!
									.moduleName ?? 'vender'
							);
						}
					},
				},
				...(JSON.parse(env.VITE_OPEN_CDN) ? { external: buildConfig.external } : {}),
			},
		},
		css: {
			preprocessorOptions: {
				css: { charset: false },
				scss: {
					additionalData: `@import "./src/theme/utils.scss";`,
					sassOptions: {
						outputStyle: 'expanded',
						sourceMap: true,
					},
				},
			},
		},
		define: {
			__NEXT_VERSION__: JSON.stringify(process.env.npm_package_version),
			__NEXT_NAME__: JSON.stringify(process.env.npm_package_name),
		},
	};
});

export default viteConfig;
